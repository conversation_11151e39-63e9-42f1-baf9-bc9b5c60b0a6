'use client';

import { But<PERSON>, Translate } from '@/components/ui';
import { useCollections } from '@/hooks';
import { useTranslation } from '@/contexts';

import { BookOpen } from 'lucide-react';
import { useState, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';
import { WordGenerationTab } from './components/word-generation-tab';
import { TabNavigation, TabType } from './components/tab-navigation';
import {
	VocabularyTranslateTab,
	VocabularyTranslateTabRef,
} from './components/vocabulary-translate-tab';


export function GenerateWordsClient() {
	const { t } = useTranslation();
	const router = useRouter();

	const {
		currentCollection,
		loading: collectionsLoading,
	} = useCollections();

	// Tab state
	const [activeTab, setActiveTab] = useState<TabType>('generate');

	// State for WordNet search loading (shared between tabs)
	const [wordNetSearchLoading, setWordNetSearchLoading] = useState<Record<string, boolean>>({});

	// Refs for cross-tab communication
	const translateTabRef = useRef<VocabularyTranslateTabRef>(null);

	// Check if any critical loading is happening
	const isCollectionsLoading = collectionsLoading.get || collectionsLoading.setCurrent;

	// Handle search WordNet term from word cards (for translation tab)
	const handleSearchWordNetTerm = useCallback(
		async (term: string) => {
			// Switch to translate tab if not already active
			if (activeTab !== 'translate') {
				setActiveTab('translate');
			}

			// Set loading state
			setWordNetSearchLoading((prev) => ({
				...prev,
				[term]: true,
			}));

			try {
				// Add word to translate form and get details
				if (translateTabRef.current) {
					await translateTabRef.current.addWordToTranslateAndGetDetails(term);
				}
			} catch (error) {
				console.error('Error adding word to translate:', error);
			} finally {
				// Clear loading state
				setWordNetSearchLoading((prev) => ({
					...prev,
					[term]: false,
				}));
			}
		},
		[activeTab, translateTabRef]
	);

	// Show loading skeleton while any critical data is loading
	if (isCollectionsLoading && !currentCollection) {
		return <PracticeSessionSkeleton type="paragraph" />;
	}

	// Collection safety check
	if (!currentCollection) return null;

	return (
		<div className="min-h-screen bg-gradient-to-br from-background to-blue-50 dark:from-background dark:to-background">
			<div className="max-w-6xl mx-auto space-y-8 py-8">
				<header className="text-center space-y-4">
					<h1 className="text-4xl font-bold text-primary dark:text-primary">
						<Translate text="words.page_title" />
					</h1>
					<p className="text-muted-foreground dark:text-muted-foreground text-lg">
						<Translate text="words.page_description" />
					</p>

					{/* View My Words Button */}
					<div className="flex justify-center pt-2">
						<Button
							variant="outline"
							onClick={() =>
								router.push(
									`/collections/${currentCollection.id}/vocabulary/my-words`
								)
							}
							className="flex items-center gap-2 text-sm font-medium"
						>
							<BookOpen className="h-4 w-4" />
							<Translate text="words.view_my_words" />
						</Button>
					</div>
				</header>

				{/* Tab Interface */}
				<TabNavigation
					activeTab={activeTab}
					onTabChange={setActiveTab}
				/>

				{/* Tab Content - Always mounted but conditionally visible */}
				<div className={`space-y-8 ${activeTab === 'generate' ? 'block' : 'hidden'}`}>
					<WordGenerationTab
						collectionId={currentCollection.id}
						onSearchWordNetTerm={handleSearchWordNetTerm}
						wordNetSearchLoading={wordNetSearchLoading}
					/>
				</div>

				<div className={`space-y-8 ${activeTab === 'translate' ? 'block' : 'hidden'}`}>
					<VocabularyTranslateTab
						ref={translateTabRef}
						collectionId={currentCollection.id}
						onSearchWordNetTerm={handleSearchWordNetTerm}
						wordNetSearchLoading={wordNetSearchLoading}
					/>
				</div>
			</div>
		</div>
	);
}
