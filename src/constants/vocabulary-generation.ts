export const VOCABULARY_GENERATION_CONFIG = {
	WORDS_PER_BATCH: 20,
	MAX_KEYWORDS: 10,
	MAX_TRANSLATIONS: 10,
	DEBOUNCE_DELAY: 300,
	INITIAL_OFFSET: 0,
	DEFAULT_SESSION_TIMEOUT: 30 * 60 * 1000, // 30 minutes
} as const;

export const VOCABULARY_GENERATION_ENDPOINTS = {
	GENERATE_WORDS: '/api/collections/{collectionId}/vocabulary/generate',
	TRANSLATE_WORDS: '/api/vocabulary-translate',
	WORD_DETAILS: '/api/llm/word-details',
	ADD_WORDS: '/api/collections/{collectionId}/words',
	REMOVE_WORDS: '/api/collections/{collectionId}/words',
} as const;

export const VOCABULARY_LOADING_STATES = {
	IDLE: 'idle',
	GENERATING: 'generating',
	TRANSLATING: 'translating',
	LOADING_MORE: 'loading_more',
	GETTING_DETAILS: 'getting_details',
	ADDING_WORD: 'adding_word',
	REMOVING_WORD: 'removing_word',
	GENERATING_EXAMPLES: 'generating_examples',
} as const;

export type VocabularyLoadingState = typeof VOCABULARY_LOADING_STATES[keyof typeof VOCABULARY_LOADING_STATES];