/**
 * Test for load more examples functionality
 * This test verifies that the offset calculation is correct and no duplicate examples are loaded
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the hook
const mockLoadMoreExamples = vi.fn();
const mockGetExampleState = vi.fn();

vi.mock('@/hooks/use-word-examples', () => ({
	useWordExamples: () => ({
		loadMoreExamples: mockLoadMoreExamples,
		getExampleState: mockGetExampleState,
	}),
}));

describe('Load More Examples', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('should calculate correct offset when loading more examples', async () => {
		// Mock initial state
		const mockExampleState = {
			examples: [
				{ id: '4', EN: 'Example 4', VI: 'Ví dụ 4' },
				{ id: '5', EN: 'Example 5', VI: 'Ví dụ 5' },
			],
			hasMore: true,
			total: 8,
			loading: false,
			error: null,
		};

		mockGetExampleState.mockReturnValue(mockExampleState);

		// Mock definition with initial examples
		const mockDefinition = {
			id: 'def-1',
			examples: [
				{ id: '1', EN: 'Example 1', VI: 'Ví dụ 1' },
				{ id: '2', EN: 'Example 2', VI: 'Ví dụ 2' },
				{ id: '3', EN: 'Example 3', VI: 'Ví dụ 3' },
			],
		};

		// Simulate the component calling loadMoreExamples
		const wordId = 'word-1';
		const definitionId = 'def-1';
		const initialExamplesCount = mockDefinition.examples.length; // 3

		await mockLoadMoreExamples(wordId, definitionId, initialExamplesCount);

		// Verify that loadMoreExamples was called with correct parameters
		expect(mockLoadMoreExamples).toHaveBeenCalledWith(
			wordId,
			definitionId,
			3 // initialExamplesCount
		);

		// The expected offset should be: initialExamplesCount (3) + currentState.examples.length (2) = 5
		// This means the API should request examples starting from index 5, not index 2
	});

	it('should not have duplicate examples when combining initial and loaded examples', () => {
		// Mock definition with initial examples (indices 0, 1, 2)
		const initialExamples = [
			{ id: '1', EN: 'Example 1', VI: 'Ví dụ 1' },
			{ id: '2', EN: 'Example 2', VI: 'Ví dụ 2' },
			{ id: '3', EN: 'Example 3', VI: 'Ví dụ 3' },
		];

		// Mock loaded examples (should start from index 3, 4, 5)
		const loadedExamples = [
			{ id: '4', EN: 'Example 4', VI: 'Ví dụ 4' },
			{ id: '5', EN: 'Example 5', VI: 'Ví dụ 5' },
			{ id: '6', EN: 'Example 6', VI: 'Ví dụ 6' },
		];

		// Combine examples as done in the component
		const allExamples = [...initialExamples, ...loadedExamples];

		// Verify no duplicates by checking unique IDs
		const uniqueIds = new Set(allExamples.map(ex => ex.id));
		expect(uniqueIds.size).toBe(allExamples.length);

		// Verify correct order and content
		expect(allExamples).toHaveLength(6);
		expect(allExamples[0].id).toBe('1');
		expect(allExamples[3].id).toBe('4');
		expect(allExamples[5].id).toBe('6');
	});

	it('should handle first load correctly', async () => {
		// Mock empty state for first load
		const mockExampleState = {
			examples: [],
			hasMore: false,
			total: 0,
			loading: false,
			error: null,
		};

		mockGetExampleState.mockReturnValue(mockExampleState);

		const wordId = 'word-1';
		const definitionId = 'def-1';
		const initialExamplesCount = 3;

		await mockLoadMoreExamples(wordId, definitionId, initialExamplesCount);

		// For first load, offset should be initialExamplesCount (3) + 0 = 3
		expect(mockLoadMoreExamples).toHaveBeenCalledWith(
			wordId,
			definitionId,
			3
		);
	});
});
