'use client';

import { Translate } from '@/components/ui';
import { Sparkles } from 'lucide-react';
import { memo } from 'react';

export type TabType = 'generate' | 'translate';

interface TabNavigationProps {
	activeTab: TabType;
	onTabChange: (tab: TabType) => void;
	className?: string;
}

function TabNavigationComponent({
	activeTab,
	onTabChange,
	className = '',
}: TabNavigationProps) {
	return (
		<div className={`w-full ${className}`}>
			<div className="grid h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground w-full grid-cols-2 mb-8">
				<button
					onClick={() => onTabChange('generate')}
					className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 gap-2 ${
						activeTab === 'generate'
							? 'bg-background text-foreground shadow'
							: ''
					}`}
				>
					<Sparkles className="h-4 w-4" />
					<Translate text="words.tabs.generate_words" />
				</button>
				<button
					onClick={() => onTabChange('translate')}
					className={`inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 gap-2 ${
						activeTab === 'translate'
							? 'bg-background text-foreground shadow'
							: ''
					}`}
				>
					<Translate text="words.tabs.translate_words" />
				</button>
			</div>
		</div>
	);
}

const arePropsEqual = (
	prevProps: TabNavigationProps,
	nextProps: TabNavigationProps
) => {
	return (
		prevProps.activeTab === nextProps.activeTab &&
		prevProps.className === nextProps.className &&
		prevProps.onTabChange === nextProps.onTabChange
	);
};

export const TabNavigation = memo(TabNavigationComponent, arePropsEqual);