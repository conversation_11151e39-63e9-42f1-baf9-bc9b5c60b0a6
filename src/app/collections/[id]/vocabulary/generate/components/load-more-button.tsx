'use client';

import { Button, Translate } from '@/components/ui';
import { Plus } from 'lucide-react';
import { memo } from 'react';

interface LoadMoreButtonProps {
	onLoadMore: () => Promise<void>;
	isLoadingMore: boolean;
	className?: string;
}

function LoadMoreButtonComponent({
	onLoadMore,
	isLoadingMore,
	className = '',
}: LoadMoreButtonProps) {
	return (
		<div className={`flex justify-center pt-6 ${className}`}>
			<Button
				variant="outline"
				size="lg"
				onClick={onLoadMore}
				loading={isLoadingMore}
				className="min-w-[200px] h-12 text-base font-medium rounded-xl border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200"
			>
				{!isLoadingMore && <Plus size={18} className="mr-2" />}
				<Translate
					text={isLoadingMore ? 'words.loading_more' : 'words.load_more'}
				/>
			</Button>
		</div>
	);
}

const arePropsEqual = (
	prevProps: LoadMoreButtonProps,
	nextProps: LoadMoreButtonProps
) => {
	return (
		prevProps.isLoadingMore === nextProps.isLoadingMore &&
		prevProps.className === nextProps.className &&
		prevProps.onLoadMore === nextProps.onLoadMore
	);
};

export const LoadMoreButton = memo(LoadMoreButtonComponent, arePropsEqual);