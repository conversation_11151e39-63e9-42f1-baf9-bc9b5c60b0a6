'use client';

import { Button, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage } from '@/lib';
import { Language } from '@prisma/client';
import { ChevronDown, Loader2, Plus } from 'lucide-react';
import { memo } from 'react';
import { useWordExamples } from '@/hooks/use-word-examples';

interface Example {
	id?: string;
	EN: string;
	VI: string;
}

interface Definition {
	id: string;
	examples: Example[];
}

interface ExamplesListProps {
	wordId: string;
	definition: Definition;
	sourceLanguage: Language;
	targetLanguage: Language;
	onGenerateExamples?: () => void;
	generatingExamples?: boolean;
}

function ExamplesListComponent({
	wordId,
	definition,
	sourceLanguage,
	targetLanguage,
	onGenerateExamples,
	generatingExamples = false,
}: ExamplesListProps) {
	const { getExampleState, loadMoreExamples } = useWordExamples();
	const exampleState = getExampleState(definition.id);

	// Combine initial examples with loaded examples
	const allExamples = [...definition.examples, ...exampleState.examples];

	const hasMoreExamples = exampleState.hasMore;
	const isFirstLoad = exampleState.examples.length === 0 && exampleState.total === 0;
	const canLoadMore = (hasMoreExamples || isFirstLoad) && !exampleState.loading && !generatingExamples;

	const handleLoadMore = async () => {
		await loadMoreExamples(wordId, definition.id);
	};

	return (
		<div>
			<div className="flex items-center justify-between mb-1.5">
				<p className="text-sm font-semibold text-muted-foreground">
					<Translate text="words.examples" />:
				</p>
			</div>

			{allExamples.length > 0 ? (
				<div className="space-y-2">
					{allExamples.map((example, exIndex) => (
						<div
							key={example.id || exIndex}
							className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
						>
							<p className="text-xs font-medium text-muted-foreground tracking-wide">
								<Translate text={getTranslationKeyOfLanguage(targetLanguage)} />:
							</p>
							<p className="mb-1 text-sm text-foreground/95">
								{example[targetLanguage] || (
									<span className="italic opacity-70">
										<Translate text="words.example_not_provided" />
									</span>
								)}
							</p>
							<p className="text-xs font-medium text-muted-foreground tracking-wide">
								<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />:
							</p>
							<p className="text-sm text-foreground/95">
								{example[sourceLanguage] || (
									<span className="italic opacity-70">
										<Translate text="words.translation_not_provided" />
									</span>
								)}
							</p>
						</div>
					))}

					{/* Load More Button */}
					{canLoadMore && (
						<div className="flex justify-center mt-3">
							<Button
								variant="outline"
								size="sm"
								onClick={handleLoadMore}
								disabled={exampleState.loading}
								className="h-8 px-3 text-xs"
							>
								{exampleState.loading ? (
									<>
										<Loader2 className="h-3 w-3 animate-spin mr-1" />
										<Translate text="words.loading_examples" />
									</>
								) : (
									<>
										<ChevronDown className="h-3 w-3 mr-1" />
										<Translate text="words.load_more_examples" />
									</>
								)}
							</Button>
						</div>
					)}

					{/* Error Message */}
					{exampleState.error && (
						<div className="text-xs text-destructive text-center mt-2">
							{exampleState.error}
						</div>
					)}

					{/* Total Count Info */}
					{exampleState.total > 0 && (
						<div className="text-xs text-muted-foreground text-center mt-2">
							<Translate text="words.showing_examples" /> {allExamples.length} /{' '}
							{exampleState.total}
						</div>
					)}
				</div>
			) : (
				<p className="text-sm text-muted-foreground italic">
					<Translate text="words.no_examples_available" />
				</p>
			)}
		</div>
	);
}

export const ExamplesList = memo(ExamplesListComponent);
