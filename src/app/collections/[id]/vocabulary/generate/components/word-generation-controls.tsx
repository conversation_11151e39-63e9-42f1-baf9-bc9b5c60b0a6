'use client';

import { <PERSON><PERSON>, LoadingSpinner, Translate } from '@/components/ui';
import { useKeywordsContext, useTranslation } from '@/contexts';
import { Sparkles } from 'lucide-react';
import { memo } from 'react';

interface WordGenerationControlsProps {
	onGenerate: () => Promise<void>;
	isGenerating: boolean;
	isLoading: boolean;
	hasErrors: boolean;
	className?: string;
}

function WordGenerationControlsComponent({
	onGenerate,
	isGenerating,
	isLoading,
	hasErrors,
	className = '',
}: WordGenerationControlsProps) {
	const { selectedKeywords } = useKeywordsContext();
	const { t } = useTranslation();

	const isDisabled = selectedKeywords.length === 0 || isLoading || hasErrors;

	return (
		<div className={`space-y-3 ${className}`}>
			{selectedKeywords.length > 0 && (
				<div className="text-sm text-primary/80 font-medium">
					<Translate
						text="words.selected_count"
						values={{ count: selectedKeywords.length }}
					/>
				</div>
			)}
			
			<Button
				className="w-full h-12 text-base font-bold rounded-2xl bg-primary text-background shadow-2xl hover:bg-primary/90 transition-all duration-200 flex gap-3 items-center justify-center"
				disabled={isDisabled}
				onClick={onGenerate}
				size="sm"
				loading={isGenerating || isLoading}
			>
				{isGenerating || isLoading ? (
					<>
						<LoadingSpinner size="sm" />
						<Translate
							text={isGenerating ? 'words.generating' : 'ui.loading'}
						/>
					</>
				) : (
					<>
						<Sparkles className="h-6 w-6" />
						<Translate text="words.generate_words" />
					</>
				)}
			</Button>
		</div>
	);
}

const arePropsEqual = (
	prevProps: WordGenerationControlsProps,
	nextProps: WordGenerationControlsProps
) => {
	return (
		prevProps.isGenerating === nextProps.isGenerating &&
		prevProps.isLoading === nextProps.isLoading &&
		prevProps.hasErrors === nextProps.hasErrors &&
		prevProps.className === nextProps.className &&
		prevProps.onGenerate === nextProps.onGenerate
	);
};

export const WordGenerationControls = memo(WordGenerationControlsComponent, arePropsEqual);