'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui';
import { cn } from '@/lib';
import { RandomWord, WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { memo, useCallback } from 'react';
import { WordDetails } from './word-details';
import { WordActions } from './word-actions';
import { LoadMoreButton } from './load-more-button';

interface WordItemProps {
	word: RandomWord;
	details: WordDetail | undefined;
	loadingState: { gettingDetail?: boolean; adding?: boolean; generatingExamples?: boolean };
	onGetDetails: (word: RandomWord) => Promise<void>;
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoWordAddition?: (word: RandomWord) => Promise<void>;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	isAdded: boolean;
	sourceLanguage: Language;
	targetLanguage: Language;
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
}

function WordItem({
	word,
	details,
	loadingState,
	onGetDetails,
	onAddToCollection,
	onUndoWordAddition,
	onGenerateExamples,
	isAdded,
	sourceLanguage,
	targetLanguage,
	onSearchWordNetTerm,
	wordNetSearchLoading = {},
}: WordItemProps) {
	return (
		<Card
			key={word.term}
			className="flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200"
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle>
					<WordDetails
						word={word}
						details={details}
						sourceLanguage={sourceLanguage}
						targetLanguage={targetLanguage}
						onGenerateExamples={onGenerateExamples}
						generatingExamples={loadingState.generatingExamples}
						onSearchWordNetTerm={onSearchWordNetTerm}
						wordNetSearchLoading={wordNetSearchLoading}
					/>
				</CardTitle>
			</CardHeader>
			<CardContent className="flex-grow flex flex-col p-5">
				<WordActions
					word={word}
					details={details}
					loadingState={loadingState}
					isAdded={isAdded}
					onGetDetails={onGetDetails}
					onAddToCollection={onAddToCollection}
					onUndoWordAddition={onUndoWordAddition}
					className="mt-auto pt-5"
				/>
			</CardContent>
		</Card>
	);
}

interface WordListProps {
	words: RandomWord[] | undefined;
	detailedWords: Record<string, WordDetail>;
	onGetDetails: (word: RandomWord) => Promise<void>;
	getLoadingState: (term: string) => {
		gettingDetail?: boolean;
		adding?: boolean;
		generatingExamples?: boolean;
	};
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoWordAddition?: (word: RandomWord) => Promise<void>;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	addedWords: Set<string>;
	className?: string;
	sourceLanguage: Language;
	targetLanguage: Language;
	// Load more functionality
	isLoadingMore?: boolean;
	onLoadMore?: () => Promise<void>;
	// WordNet search functionality
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
}

function WordListComponent({
	words,
	detailedWords,
	onGetDetails,
	getLoadingState,
	onAddToCollection,
	onUndoWordAddition,
	onGenerateExamples,
	addedWords,
	className,
	sourceLanguage, // Default fallback
	targetLanguage, // Default fallback
	isLoadingMore = false,
	onLoadMore,
	onSearchWordNetTerm,
	wordNetSearchLoading = {},
}: WordListProps) {
	// Capture callback to avoid closure issues
	const searchCallback = onSearchWordNetTerm;
	const handleGetDetails = useCallback(
		async (word: RandomWord) => {
			if (detailedWords[word.term] || getLoadingState(word.term).gettingDetail) return;
			await onGetDetails(word);
		},
		[detailedWords, getLoadingState, onGetDetails]
	);

	const handleAddToCollection = useCallback(
		async (word: RandomWord) => {
			if (getLoadingState(word.term).adding) return;
			await onAddToCollection(word);
		},
		[getLoadingState, onAddToCollection]
	);

	if (!words || words.length === 0) return null;

	return (
		<div className={cn('space-y-4', className)}>
			{words?.map((word) => {
				const loadingState = getLoadingState(word.term);
				const details = detailedWords[word.term];

				return (
					<WordItem
						key={word.term}
						word={word}
						details={details}
						loadingState={loadingState}
						onGetDetails={handleGetDetails}
						onAddToCollection={handleAddToCollection}
						onUndoWordAddition={onUndoWordAddition}
						onGenerateExamples={onGenerateExamples}
						isAdded={addedWords.has(word.term)}
						sourceLanguage={sourceLanguage}
						targetLanguage={targetLanguage}
						onSearchWordNetTerm={searchCallback}
						wordNetSearchLoading={wordNetSearchLoading}
					/>
				);
			})}

			{/* Load More Button - Always show if onLoadMore is provided */}
			{onLoadMore && (
				<LoadMoreButton
					onLoadMore={onLoadMore}
					isLoadingMore={isLoadingMore}
				/>
			)}
		</div>
	);
}

const arePropsEqual = (prevProps: WordListProps, nextProps: WordListProps) => {
	return (
		(prevProps.words?.length ?? 0) === (nextProps.words?.length ?? 0) &&
		(prevProps.words?.every((word, index) => word.term === nextProps.words?.[index]?.term) ??
			true) &&
		Object.keys(prevProps.detailedWords).length ===
			Object.keys(nextProps.detailedWords).length &&
		prevProps.className === nextProps.className &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.isLoadingMore === nextProps.isLoadingMore &&
		// Shallow compare functions assuming they are stable (e.g., from useCallback with stable deps)
		prevProps.onGetDetails === nextProps.onGetDetails &&
		prevProps.getLoadingState === nextProps.getLoadingState &&
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onUndoWordAddition === nextProps.onUndoWordAddition &&
		prevProps.onLoadMore === nextProps.onLoadMore &&
		prevProps.onSearchWordNetTerm === nextProps.onSearchWordNetTerm &&
		prevProps.wordNetSearchLoading === nextProps.wordNetSearchLoading
	);
};

export const WordList = memo(WordListComponent, arePropsEqual);
