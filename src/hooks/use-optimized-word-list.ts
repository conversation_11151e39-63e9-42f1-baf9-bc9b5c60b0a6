import { useMemo, useCallback, useRef, useEffect } from 'react';
import { RandomWord, WordDetail } from '@/models';
import { Language } from '@prisma/client';

export interface OptimizedWordItem {
	id: string;
	word: RandomWord;
	details?: WordDetail;
	isAdded: boolean;
	loadingState: {
		adding: boolean;
		gettingDetail: boolean;
		generatingExamples: boolean;
	};
}

interface UseOptimizedWordListProps {
	words: RandomWord[];
	detailedWords: Record<string, WordDetail>;
	addedWords: Set<string>;
	getLoadingState: (term: string) => {
		adding?: boolean;
		gettingDetail?: boolean;
		generatingExamples?: boolean;
	};
	sourceLanguage: Language;
	targetLanguage: Language;
}

export function useOptimizedWordList({
	words,
	detailedWords,
	addedWords,
	getLoadingState,
	sourceLanguage,
	targetLanguage,
}: UseOptimizedWordListProps) {
	// Memoize word items with normalized data structure
	const optimizedWords = useMemo<OptimizedWordItem[]>(() => {
		return words.map((word) => ({
			id: `${word.term}-${sourceLanguage}-${targetLanguage}`,
			word,
			details: detailedWords[word.term],
			isAdded: addedWords.has(word.term),
			loadingState: {
				adding: false,
				gettingDetail: false,
				generatingExamples: false,
				...getLoadingState(word.term),
			},
		}));
	}, [words, detailedWords, addedWords, getLoadingState, sourceLanguage, targetLanguage]);

	// Create stable references for callback functions
	const stableCallbacks = useRef<{
		onGetDetails?: (word: RandomWord) => Promise<void>;
		onAddToCollection?: (word: RandomWord) => Promise<void>;
		onUndoWordAddition?: (word: RandomWord) => Promise<void>;
		onGenerateExamples?: (word: RandomWord) => Promise<void>;
		onSearchWordNetTerm?: (term: string) => void;
	}>({});

	const createStableCallback = useCallback(
		<T extends (...args: any[]) => any>(callback: T | undefined): T | undefined => {
			if (!callback) return undefined;
			
			// Use the same function reference if the callback hasn't changed
			const currentCallback = stableCallbacks.current[callback.name as keyof typeof stableCallbacks.current];
			if (currentCallback === callback) {
				return currentCallback as T;
			}
			
			stableCallbacks.current[callback.name as keyof typeof stableCallbacks.current] = callback;
			return callback;
		},
		[]
	);

	// Memoize word items by term for O(1) lookups
	const wordMap = useMemo(() => {
		const map = new Map<string, OptimizedWordItem>();
		optimizedWords.forEach((item) => {
			map.set(item.word.term, item);
		});
		return map;
	}, [optimizedWords]);

	// Virtual scrolling helper - calculate visible items
	const getVisibleItems = useCallback(
		(startIndex: number, endIndex: number): OptimizedWordItem[] => {
			return optimizedWords.slice(startIndex, Math.min(endIndex, optimizedWords.length));
		},
		[optimizedWords]
	);

	// Batch update helper for loading states
	const batchUpdateLoadingStates = useCallback(
		(updates: Array<{ term: string; state: Partial<OptimizedWordItem['loadingState']> }>) => {
			// This would be used with a reducer pattern in the parent component
			return updates.reduce((acc, { term, state }) => {
				const item = wordMap.get(term);
				if (item) {
					acc[term] = { ...item.loadingState, ...state };
				}
				return acc;
			}, {} as Record<string, OptimizedWordItem['loadingState']>);
		},
		[wordMap]
	);

	// Performance metrics
	const metrics = useMemo(() => ({
		totalWords: optimizedWords.length,
		addedWords: optimizedWords.filter(item => item.isAdded).length,
		wordsWithDetails: optimizedWords.filter(item => item.details).length,
		loadingWords: optimizedWords.filter(item => 
			item.loadingState.adding || 
			item.loadingState.gettingDetail || 
			item.loadingState.generatingExamples
		).length,
	}), [optimizedWords]);

	return {
		optimizedWords,
		wordMap,
		getVisibleItems,
		batchUpdateLoadingStates,
		createStableCallback,
		metrics,
	};
}