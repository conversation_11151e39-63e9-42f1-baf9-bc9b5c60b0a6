import { Language } from '@prisma/client';
import { RandomWord, WordDetail } from '@/models';
import { VOCABULARY_GENERATION_CONFIG, VOCABULARY_GENERATION_ENDPOINTS } from '@/constants/vocabulary-generation';
import { VocabularyGenerationError, WordAdditionError, VOCABULARY_ERROR_MESSAGES } from '@/lib/vocabulary-errors';

export interface GenerateWordsParams {
	collectionId: string;
	keywords: string[];
	maxTerms: number;
	excludeTerms: string[];
	sourceLanguage: Language;
	targetLanguage: Language;
	offset?: number;
}

export interface AddWordResult {
	words: Array<{
		id: string;
		term: string;
	}>;
}

export interface VocabularyGenerationService {
	generateWords(params: GenerateWordsParams): Promise<RandomWord[]>;
	loadMoreWords(params: GenerateWordsParams): Promise<RandomWord[]>;
	addWordToCollection(collectionId: string, term: string, language: Language): Promise<AddWordResult>;
	removeWordsFromCollection(collectionId: string, wordIds: string[]): Promise<void>;
}

class VocabularyGenerationServiceImpl implements VocabularyGenerationService {
	async generateWords(params: GenerateWordsParams): Promise<RandomWord[]> {
		try {
			const endpoint = VOCABULARY_GENERATION_ENDPOINTS.GENERATE_WORDS.replace(
				'{collectionId}',
				params.collectionId
			);

			const response = await fetch(endpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					keywords: params.keywords,
					maxTerms: params.maxTerms,
					excludeTerms: params.excludeTerms,
					sourceLanguage: params.sourceLanguage,
					targetLanguage: params.targetLanguage,
					offset: params.offset ?? VOCABULARY_GENERATION_CONFIG.INITIAL_OFFSET,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new VocabularyGenerationError(
					error.error || VOCABULARY_ERROR_MESSAGES.GENERATION_FAILED
				);
			}

			return response.json();
		} catch (error) {
			if (error instanceof VocabularyGenerationError) {
				throw error;
			}
			throw new VocabularyGenerationError(
				VOCABULARY_ERROR_MESSAGES.GENERATION_FAILED,
				error instanceof Error ? error : new Error(String(error))
			);
		}
	}

	async loadMoreWords(params: GenerateWordsParams): Promise<RandomWord[]> {
		try {
			const moreWordsParams = {
				...params,
				maxTerms: VOCABULARY_GENERATION_CONFIG.WORDS_PER_BATCH,
			};
			
			return await this.generateWords(moreWordsParams);
		} catch (error) {
			throw new VocabularyGenerationError(
				'words.load_more_failed',
				error instanceof Error ? error : new Error(String(error))
			);
		}
	}

	async addWordToCollection(collectionId: string, term: string, language: Language): Promise<AddWordResult> {
		try {
			const endpoint = VOCABULARY_GENERATION_ENDPOINTS.ADD_WORDS.replace(
				'{collectionId}',
				collectionId
			);

			const response = await fetch(endpoint, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					term,
					language,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new WordAdditionError(
					error.error || VOCABULARY_ERROR_MESSAGES.WORD_ADD_FAILED
				);
			}

			return response.json();
		} catch (error) {
			if (error instanceof WordAdditionError) {
				throw error;
			}
			throw new WordAdditionError(
				VOCABULARY_ERROR_MESSAGES.WORD_ADD_FAILED,
				error instanceof Error ? error : new Error(String(error))
			);
		}
	}

	async removeWordsFromCollection(collectionId: string, wordIds: string[]): Promise<void> {
		try {
			const endpoint = VOCABULARY_GENERATION_ENDPOINTS.REMOVE_WORDS.replace(
				'{collectionId}',
				collectionId
			);

			const response = await fetch(endpoint, {
				method: 'DELETE',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					wordIds,
				}),
			});

			if (!response.ok) {
				const error = await response.json();
				throw new WordAdditionError(
					error.error || 'Failed to remove words from collection'
				);
			}
		} catch (error) {
			if (error instanceof WordAdditionError) {
				throw error;
			}
			throw new WordAdditionError(
				'Failed to remove words from collection',
				error instanceof Error ? error : new Error(String(error))
			);
		}
	}
}

let vocabularyGenerationServiceInstance: VocabularyGenerationService | null = null;

export function getVocabularyGenerationService(): VocabularyGenerationService {
	if (!vocabularyGenerationServiceInstance) {
		vocabularyGenerationServiceInstance = new VocabularyGenerationServiceImpl();
	}
	return vocabularyGenerationServiceInstance;
}