export class VocabularyGenerationError extends Error {
	constructor(message: string, public cause?: Error) {
		super(message);
		this.name = 'VocabularyGenerationError';
	}
}

export class WordTranslationError extends Error {
	constructor(message: string, public cause?: Error) {
		super(message);
		this.name = 'WordTranslationError';
	}
}

export class WordAdditionError extends Error {
	constructor(message: string, public cause?: Error) {
		super(message);
		this.name = 'WordAdditionError';
	}
}

export class WordDetailsError extends Error {
	constructor(message: string, public cause?: Error) {
		super(message);
		this.name = 'WordDetailsError';
	}
}

export class ExampleGenerationError extends Error {
	constructor(message: string, public cause?: Error) {
		super(message);
		this.name = 'ExampleGenerationError';
	}
}

export const VOCABULARY_ERROR_MESSAGES = {
	GENERATION_FAILED: 'words.generation_failed',
	TRANSLATION_FAILED: 'vocabulary_translate.translation_failed',
	WORD_ADD_FAILED: 'words.add_error',
	WORD_DETAILS_FAILED: 'words.detail_fetch_error',
	EXAMPLES_FAILED: 'words.examples_failed',
	NETWORK_ERROR: 'errors.network_error',
	INVALID_PARAMETERS: 'errors.invalid_parameters',
	COLLECTION_NOT_FOUND: 'collections.not_found',
} as const;