'use client';

import { Badge, Translate } from '@/components/ui';
import { ExamplesList } from '@/components/examples-list';
import { WordNetInfo, WordNetSummary } from '@/components/wordnet';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { RandomWord, WordDetail, WordNetData } from '@/models';
import { Language } from '@prisma/client';
import { memo } from 'react';

interface WordDetailsProps {
	word: RandomWord;
	details: WordDetail | undefined;
	sourceLanguage: Language;
	targetLanguage: Language;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	generatingExamples?: boolean;
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
	className?: string;
}

function WordDetailsComponent({
	word,
	details,
	sourceLanguage,
	targetLanguage,
	onGenerateExamples,
	generatingExamples = false,
	onSearchWordNetTerm,
	wordNetSearchLoading = {},
	className = '',
}: WordDetailsProps) {
	return (
		<div className={cn('space-y-4', className)}>
			{/* Header with term and part of speech */}
			<div className="flex-1">
				<h3 className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm mb-2">
					{word.term}
				</h3>
				<div className="flex flex-wrap items-center gap-2 mb-2">
					{word.partOfSpeech && word.partOfSpeech.length > 0 && (
						<div className="flex flex-wrap gap-1">
							{word.partOfSpeech.map((pos, index) => (
								<Badge
									key={index}
									variant="secondary"
									className="text-xs font-medium"
								>
									{pos}
								</Badge>
							))}
						</div>
					)}
					{/* WordNet Summary for detailed words */}
					{details?.WordNetData && (
						<WordNetSummary wordNetData={details.WordNetData} />
					)}
				</div>
			</div>

			{/* Basic meaning from RandomWord (when no details) */}
			{!details && word.meaning && word.meaning.length > 0 && (
				<div className="space-y-2">
					{word.meaning.map((meaning, index) => (
						<div
							key={index}
							className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
						>
							<div className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1">
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate
										text={getTranslationKeyOfLanguage(targetLanguage)}
									/>
									:
								</p>
								<p className="mb-1 text-sm text-foreground/95">
									{meaning[targetLanguage] || (
										<span className="italic opacity-70">
											<Translate text="words.meaning_not_provided" />
										</span>
									)}
								</p>
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate
										text={getTranslationKeyOfLanguage(sourceLanguage)}
									/>
									:
								</p>
								<p className="text-sm text-foreground/95">
									{meaning[sourceLanguage] || (
										<span className="italic opacity-70">
											<Translate text="words.translation_not_provided" />
										</span>
									)}
								</p>
							</div>
						</div>
					))}
				</div>
			)}

			{/* Detailed information from WordDetail */}
			{details && details.definitions && details.definitions.length > 0
				? details.definitions.map((definition, index) => (
						<div
							key={index}
							className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
						>
							{definition.pos && definition.pos.length > 0 && (
								<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
									{definition.pos.join(', ')}
								</p>
							)}
							{definition.ipa && (
								<p className="text-sm text-muted-foreground italic mb-2.5">
									IPA: {definition.ipa}
								</p>
							)}
							{/* Explanations section */}
							{definition.explains && definition.explains.length > 0 ? (
								<div className="mb-3">
									<p className="text-sm font-semibold text-muted-foreground mb-1.5">
										<Translate text="words.explanations" />:
									</p>
									{definition.explains.map((explain, expIndex) => (
										<div
											key={expIndex}
											className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
										>
											<p className="text-xs font-medium text-muted-foreground tracking-wide">
												<Translate
													text={getTranslationKeyOfLanguage(
														targetLanguage
													)}
												/>
												:
											</p>
											<p className="mb-1 text-sm text-foreground/95">
												{explain[targetLanguage] || (
													<span className="italic opacity-70">
														<Translate text="words.explanation_not_provided" />
													</span>
												)}
											</p>
											<p className="text-xs font-medium text-muted-foreground tracking-wide">
												<Translate
													text={getTranslationKeyOfLanguage(
														sourceLanguage
													)}
												/>
												:
											</p>
											<p className="text-sm text-foreground/95">
												{explain[sourceLanguage] || (
													<span className="italic opacity-70">
														<Translate text="words.translation_not_provided" />
													</span>
												)}
											</p>
										</div>
									))}
								</div>
							) : (
								<div className="mb-3">
									<p className="text-sm font-semibold text-muted-foreground mb-1.5">
										<Translate text="words.explanations" />:
									</p>
									<p className="p-2 text-sm text-muted-foreground italic opacity-70">
										<Translate text="words.no_explanations_provided" />
									</p>
								</div>
							)}

							{/* Examples section */}
							<ExamplesList
								wordId={details?.id || ''}
								definition={definition}
								sourceLanguage={sourceLanguage}
								targetLanguage={targetLanguage}
								onGenerateExamples={() => onGenerateExamples?.(word)}
								generatingExamples={generatingExamples}
							/>
						</div>
				))
				: details && (
						<p className="p-4 text-sm text-muted-foreground italic">
							<Translate text="words.no_definitions_available" />
						</p>
				)}

			{/* WordNet Information */}
			{(details?.WordNetData || word.wordnet_data) && (
				<div className="mt-4">
					<WordNetInfo
						wordNetData={(details?.WordNetData || word.wordnet_data) as WordNetData}
						term={word.term}
						className="border-0 shadow-none bg-transparent p-0"
						onSearchTerm={(term) => {
							onSearchWordNetTerm?.(term);
						}}
						searchLoading={wordNetSearchLoading}
					/>
				</div>
			)}
		</div>
	);
}

const arePropsEqual = (prevProps: WordDetailsProps, nextProps: WordDetailsProps) => {
	return (
		prevProps.word.term === nextProps.word.term &&
		prevProps.details?.id === nextProps.details?.id &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.generatingExamples === nextProps.generatingExamples &&
		prevProps.className === nextProps.className &&
		prevProps.onGenerateExamples === nextProps.onGenerateExamples &&
		prevProps.onSearchWordNetTerm === nextProps.onSearchWordNetTerm &&
		prevProps.wordNetSearchLoading === nextProps.wordNetSearchLoading
	);
};

export const WordDetails = memo(WordDetailsComponent, arePropsEqual);