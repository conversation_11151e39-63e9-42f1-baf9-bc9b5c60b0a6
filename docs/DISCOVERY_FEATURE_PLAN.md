# 🔍 DISCOVERY FEATURE - KẾ HOẠCH THỰC HIỆN TÍNH NĂNG GIỐNG TIKTOK

## 📋 TỔNG QUAN DỰ ÁN

Discovery Feature là tính năng mới cho phép người dùng khám phá từ vựng một cách tương tác và thú vị, tương tự như trải nghiệm TikTok/Instagram Reels/YouTube Shorts. Tính năng này sẽ hiển thị từ vựng gợi ý theo dạng full-screen vertical feed với các tương tác like (tim) và save (lưu).

## 🎯 MỤC TIÊU CHÍNH

1. **Tăng engagement**: Tạo trải nghiệm học tập thú vị và addictive
2. **Khám phá từ vựng**: Giúp người dùng khám phá từ mới phù hợp với trình độ
3. **C<PERSON> nhân hóa**: <PERSON>ợ<PERSON> ý thông minh dựa trên sở thích và tiến độ học tập
4. **Tương tác nhanh**: Cho phép lưu từ vào collection hoặc wishlist một cách nhanh chóng

## 📱 LUỒNG NGƯỜI DÙNG CHI TIẾT

### 1. Khởi tạo Discovery Mode

1. **Trên trang chủ**: Người dùng nhìn thấy nút "Discovery" nổi bật cạnh nút "Collections"
2. **Nhấn Discovery**: Dialog toàn màn hình mở ra với animation smooth
3. **Loading**: Hiển thị skeleton loading trong khi fetch 10 từ đầu tiên
4. **Hiển thị từ đầu tiên**: Card từ vựng xuất hiện với animation fade-in

### 2. Tương tác với từ vựng

1. **Xem từ**: Hiển thị từ, phát âm, định nghĩa và ví dụ
2. **Navigation**:
    - Swipe up/down hoặc arrow keys để chuyển từ
    - Smooth transition giữa các từ
3. **Actions**:
    - **Nút tim (♡)**: Like và tự động lưu vào wishlist collection
    - **Nút bookmark (📚)**: Mở modal chọn collection để lưu

### 3. Lưu từ vào Collection

1. **Like (Tim)**: Tự động lưu vào collection wishlist mặc định
2. **Save (Bookmark)**:
    - Mở modal hiển thị danh sách collection tương thích
    - Filter theo source_language và target_language
    - Hiển thị wishlist collection (không filter ngôn ngữ)
    - Chọn collection và lưu từ

### 4. Wishlist Collection Đặc biệt

-   **Tự động tạo**: Mỗi user có 1 collection wishlist mặc định
-   **Không giới hạn ngôn ngữ**: Có thể lưu từ bất kỳ ngôn ngữ nào
-   **Type**: WISHLIST (khác với REGULAR collection)
-   **Tên mặc định**: "My Wishlist" hoặc "Danh sách yêu thích"

## 🏗️ KIẾN TRÚC TỔNG THỂ

### 1. Database Schema Changes

#### 1.1 Thêm Collection Type cho Wishlist

#### 1.2 Discovery Interaction Tracking

```sql
-- Bảng tracking tương tác discovery
CREATE TABLE "DiscoveryInteraction" (
  "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  "user_id" TEXT NOT NULL,
  "word_id" TEXT NOT NULL,
  "action" TEXT NOT NULL, -- 'LIKE', 'SAVE', 'SKIP', 'VIEW'
  "collection_id" TEXT, -- null for LIKE (goes to default wishlist)
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW(),

  FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE,
  FOREIGN KEY ("word_id") REFERENCES "Word"("id") ON DELETE CASCADE,
  FOREIGN KEY ("collection_id") REFERENCES "Collection"("id") ON DELETE SET NULL
);

CREATE INDEX "idx_discovery_interaction_user" ON "DiscoveryInteraction"("user_id");
CREATE INDEX "idx_discovery_interaction_word" ON "DiscoveryInteraction"("word_id");
CREATE INDEX "idx_discovery_interaction_action" ON "DiscoveryInteraction"("action");
```

#### 1.3 User Preferences for Discovery

```sql
-- Bảng lưu preferences cho discovery algorithm
CREATE TABLE "DiscoveryPreference" (
  "id" TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
  "user_id" TEXT NOT NULL,
  "preferred_languages" TEXT[] DEFAULT '{}', -- ['EN', 'VI']
  "difficulty_preference" TEXT DEFAULT 'INTERMEDIATE', -- BEGINNER, INTERMEDIATE, ADVANCED
  "categories" TEXT[] DEFAULT '{}', -- ['business', 'travel', 'academic']
  "avoid_words" TEXT[] DEFAULT '{}', -- words user wants to skip
  "created_at" TIMESTAMP DEFAULT NOW(),
  "updated_at" TIMESTAMP DEFAULT NOW(),

  FOREIGN KEY ("user_id") REFERENCES "User"("id") ON DELETE CASCADE
);

CREATE UNIQUE INDEX "unique_discovery_preference_per_user" ON "DiscoveryPreference"("user_id");
```

### 2. Backend Services Architecture

#### 2.1 Discovery Service

```typescript
// src/backend/services/discovery.service.ts
export interface DiscoveryService {
	// Lấy danh sách từ gợi ý cho user
	getRecommendedWords(userId: string, options: DiscoveryOptions): Promise<DiscoveryWord[]>;

	// Xử lý tương tác (like, save, skip)
	handleInteraction(userId: string, interaction: DiscoveryInteraction): Promise<void>;

	// Lấy collection phù hợp để save
	getCompatibleCollections(userId: string, wordId: string): Promise<Collection[]>;

	// Tạo/lấy default wishlist
	getOrCreateDefaultWishlist(userId: string): Promise<Collection>;

	// Cập nhật preferences
	updatePreferences(userId: string, preferences: DiscoveryPreference): Promise<void>;
}

export interface DiscoveryOptions {
	limit?: number; // default 10
	excludeWordIds?: string[]; // từ đã xem
	sourceLanguage?: Language;
	targetLanguage?: Language;
}

export interface DiscoveryWord {
	id: string;
	term: string;
	language: Language;
	definitions: Definition[];
	difficulty?: string;
	category?: string;
	isLiked?: boolean;
	isSaved?: boolean;
	compatibleCollections: Collection[];
}
```

#### 2.2 Recommendation Algorithm Service

```typescript
// src/backend/services/recommendation.service.ts
export interface RecommendationService {
	// Thuật toán gợi ý chính
	generateRecommendations(
		userId: string,
		options: RecommendationOptions
	): Promise<WordRecommendation[]>;

	// Cập nhật model dựa trên interaction
	updateUserProfile(userId: string, interaction: UserInteraction): Promise<void>;

	// Phân tích xu hướng user
	analyzeUserPreferences(userId: string): Promise<UserPreferenceProfile>;
}

interface RecommendationOptions {
	limit: number;
	excludeIds: string[];
	diversityFactor: number; // 0-1, balance giữa personalization và diversity
	freshnessFactor: number; // 0-1, ưu tiên từ mới vs từ phù hợp
}

interface WordRecommendation {
	wordId: string;
	score: number;
	reasons: string[]; // ['similar_to_liked', 'trending', 'difficulty_match']
	category: string;
}
```

### 3. API Endpoints

#### 3.1 Discovery API

```typescript
// GET /api/discovery/words
// Query params: limit, exclude, sourceLanguage, targetLanguage
interface GetDiscoveryWordsResponse {
	words: DiscoveryWord[];
	hasMore: boolean;
	totalAvailable: number;
}

// POST /api/discovery/interact
interface DiscoveryInteractionRequest {
	wordId: string;
	action: 'LIKE' | 'SAVE' | 'SKIP' | 'VIEW';
	collectionId?: string; // required for SAVE action
}

// GET /api/discovery/collections/{wordId}
// Trả về danh sách collection phù hợp để save word
interface GetCompatibleCollectionsResponse {
	collections: Collection[];
	defaultWishlist: Collection;
}

// POST /api/discovery/preferences
interface UpdatePreferencesRequest {
	preferredLanguages: Language[];
	difficultyPreference: Difficulty;
	categories: string[];
	avoidWords: string[];
}
```

### 4. Frontend Components Architecture

#### 4.1 Discovery Dialog Component

```typescript
// src/components/discovery/discovery-dialog.tsx
interface DiscoveryDialogProps {
	isOpen: boolean;
	onClose: () => void;
}

// Features:
// - Full-screen modal overlay
// - Vertical swipeable cards
// - Touch/gesture support
// - Keyboard navigation
// - Loading states
// - Error handling
```

#### 4.2 Discovery Card Component

```typescript
// src/components/discovery/discovery-card.tsx
interface DiscoveryCardProps {
	word: DiscoveryWord;
	onLike: (wordId: string) => void;
	onSave: (wordId: string, collectionId?: string) => void;
	onSkip: (wordId: string) => void;
	isVisible: boolean; // for analytics tracking
}

// Features:
// - Word pronunciation audio
// - Definition display with examples
// - Smooth animations
// - Interactive buttons (heart, bookmark)
// - Collection selection modal
```

#### 4.3 Collection Selection Modal

```typescript
// src/components/discovery/collection-selection-modal.tsx
interface CollectionSelectionModalProps {
	word: DiscoveryWord;
	collections: Collection[];
	defaultWishlist: Collection;
	onSelect: (collectionId: string) => void;
	onClose: () => void;
}

// Features:
// - Filter collections by language compatibility
// - Quick save to wishlist option
// - Create new collection option
// - Search/filter collections
```

## 🎨 UI/UX DESIGN

### 1. Discovery Button on Home Page

-   **Vị trí**: Thêm button "Discovery" cạnh button "Collections" hiện tại
-   **Icon**: Compass hoặc Sparkles icon
-   **Style**: Gradient background để nổi bật
-   **Animation**: Subtle pulse effect để thu hút attention

### 2. Full-Screen Discovery Interface

```
┌─────────────────────────────────┐
│ [X]                    [•••]    │ <- Header với close button và menu
│                                 │
│          WORD CARD              │
│                                 │
│     ┌─────────────────────┐     │
│     │                     │     │
│     │    "SERENDIPITY"    │     │ <- Large word display
│     │                     │     │
│     │   /ˌserənˈdɪpɪti/   │     │ <- Pronunciation
│     │                     │     │
│     │  The pleasant       │     │ <- Definition
│     │  surprise of        │     │
│     │  finding something  │     │
│     │  you weren't        │     │
│     │  looking for...     │     │
│     │                     │     │
│     │  Examples:          │     │ <- Examples
│     │  "A serendipitous   │     │
│     │   discovery"        │     │
│     │                     │     │
│     └─────────────────────┘     │
│                                 │
│              ♡      📚          │ <- Action buttons (Like, Save)
│                                 │
│     ────────── 3/10 ──────────  │ <- Progress indicator
└─────────────────────────────────┘
```

### 3. Interactive Elements

-   **Swipe Gestures**:
    -   Swipe up: Next word
    -   Swipe down: Previous word
    -   Swipe left: Skip word
    -   Swipe right: Like word
-   **Touch Buttons**:
    -   Heart (♡): Like and save to wishlist
    -   Bookmark (📚): Open collection selection
    -   Skip (→): Move to next word
-   **Keyboard Support**:
    -   Space/Enter: Like word
    -   S: Open save modal
    -   Escape: Close dialog
    -   Arrow keys: Navigate

## 🧠 RECOMMENDATION ALGORITHM

### 1. Scoring System

```typescript
interface WordScore {
	baseScore: number; // 0-100
	personalityFactor: number; // Dựa trên user behavior
	difficultyMatch: number; // Phù hợp với level user
	categoryRelevance: number; // Thuộc category user quan tâm
	freshnessFactor: number; // Từ mới vs từ quen thuộc
	trendingFactor: number; // Từ đang "hot"
	diversityPenalty: number; // Tránh lặp lại cùng pattern
	finalScore: number; // Tổng điểm cuối cùng
}
```

### 2. User Behavior Analysis

```typescript
interface UserBehaviorProfile {
	preferredDifficulties: Difficulty[];
	likedCategories: string[];
	skipPatterns: string[]; // Patterns của từ user hay skip
	engagementTimes: number[]; // Thời gian tương tác với mỗi từ
	learningVelocity: number; // Tốc độ học của user
	retentionRate: number; // Tỷ lệ nhớ từ đã học
}
```

### 3. Content-Based Filtering

-   **Semantic Similarity**: Sử dụng word embeddings để tìm từ tương tự
-   **Difficulty Progression**: Dần dần tăng độ khó dựa trên progress
-   **Category Clustering**: Nhóm từ theo chủ đề (business, travel, academic)
-   **WordNet Relations**: Sử dụng hyponyms, hypernyms để mở rộng vocabulary

### 4. Collaborative Filtering

-   **User Similarity**: Tìm user có pattern học tập tương tự
-   **Popular Among Similar Users**: Từ được like nhiều bởi user tương tự
-   **Trending Words**: Từ đang được học nhiều trong community

### 5. Cold Start Problem Solutions

-   **New Users**:
    -   Onboarding quiz để xác định level
    -   Sử dụng popular words trong mỗi category
    -   Progressive difficulty adjustment
-   **New Words**:
    -   Bootstrap với basic popularity score
    -   Semantic similarity với existing words
    -   A/B testing với small user groups

## 🔧 IMPLEMENTATION PHASES

### Phase 1: Core Infrastructure (Week 1-2)

1. **Database Schema Setup**

    - Tạo tables mới
    - Update existing schema
    - Migration scripts
    - Seed data for testing

2. **Basic Backend Services**

    - DiscoveryService cơ bản
    - API endpoints cho CRUD operations
    - Integration với existing services

3. **Default Wishlist Implementation**
    - Auto-create wishlist cho existing users
    - Wishlist cho new users
    - Business logic cho wishlist

### Phase 2: Recommendation Engine (Week 3-4)

1. **Basic Recommendation Algorithm**

    - Content-based filtering
    - Simple scoring system
    - Integration với LLM services

2. **User Preference System**

    - Preference collection
    - Behavior tracking
    - Profile analysis

3. **API Integration**
    - Connect recommendation với discovery API
    - Performance optimization
    - Caching strategy

### Phase 3: Frontend Development (Week 5-6)

1. **Discovery Dialog Component**

    - Full-screen modal
    - Basic card display
    - Navigation logic

2. **Word Card Components**

    - Word display với pronunciation
    - Definition và examples
    - Interactive buttons

3. **Collection Selection Modal**
    - Collection filtering
    - Quick save features
    - New collection creation

### Phase 4: Enhanced UX (Week 7-8)

1. **Gesture Support**

    - Touch/swipe gestures
    - Keyboard navigation
    - Accessibility features

2. **Animations & Transitions**

    - Card transitions
    - Button animations
    - Loading states

3. **Advanced Features**
    - Audio pronunciation
    - Progress tracking
    - Analytics integration

### Phase 5: Optimization & Testing (Week 9-10)

1. **Performance Optimization**

    - Lazy loading
    - Caching optimization
    - Bundle size optimization

2. **A/B Testing Setup**

    - Different recommendation algorithms
    - UI variations testing
    - Conversion tracking

3. **Quality Assurance**
    - Comprehensive testing
    - Cross-browser compatibility
    - Mobile responsiveness

## 📊 SUCCESS METRICS

### 1. Engagement Metrics

-   **Session Duration**: Thời gian user spend trong discovery mode
-   **Words Per Session**: Số từ user xem trong một session
-   **Return Rate**: Tỷ lệ user quay lại sử dụng discovery
-   **Daily Active Users**: Số user sử dụng discovery hàng ngày

### 2. Learning Metrics

-   **Save Rate**: Tỷ lệ từ được save vào collection
-   **Like Rate**: Tỷ lệ từ được like
-   **Retention Rate**: Tỷ lệ user nhớ từ đã học qua discovery
-   **Progression Rate**: Tốc độ tiến bộ của user

### 3. Business Metrics

-   **User Retention**: Tỷ lệ user tiếp tục sử dụng app
-   **Feature Adoption**: Tỷ lệ user sử dụng discovery feature
-   **Collection Growth**: Số lượng từ trong collections tăng
-   **Premium Conversion**: Tỷ lệ chuyển đổi sang premium (nếu có)

## 🚀 FUTURE ENHANCEMENTS

### 1. Social Features

-   **Share Words**: Chia sẻ từ hay với friends
-   **Social Learning**: Xem từ friends đang học
-   **Leaderboards**: Bảng xếp hạng học tập

### 2. Advanced AI Features

-   **Voice Recognition**: Practice pronunciation
-   **Contextual Learning**: Từ theo context cụ thể
-   **Adaptive Difficulty**: AI tự động adjust difficulty

### 3. Gamification

-   **Streak System**: Chuỗi ngày học liên tục
-   **Achievements**: Badges cho milestones
-   **Challenges**: Weekly vocabulary challenges

### 4. Content Expansion

-   **Phrase Discovery**: Khám phá phrases và idioms
-   **Story Mode**: Từ vựng trong context stories
-   **Video Integration**: Từ vựng với video examples

## ⚠️ TECHNICAL CONSIDERATIONS

### 1. Performance

-   **Lazy Loading**: Load words theo batch
-   **Caching Strategy**: Cache recommendations và user data
-   **Database Optimization**: Index cho queries phức tạp
-   **CDN**: Cache static assets

### 2. Scalability

-   **Horizontal Scaling**: Support multiple app instances
-   **Database Sharding**: Phân tán data khi cần
-   **Queue System**: Background processing cho analytics
-   **Microservices**: Tách discovery thành service riêng

### 3. Security & Privacy

-   **Data Privacy**: Encrypt user behavior data
-   **Rate Limiting**: Prevent API abuse
-   **Content Moderation**: Filter inappropriate content
-   **User Consent**: Clear privacy policies

### 4. Monitoring & Analytics

-   **Performance Monitoring**: Track API response times
-   **Error Tracking**: Monitor và alert errors
-   **User Analytics**: Track user behavior patterns
-   **A/B Testing Infrastructure**: Sophisticated testing framework

---

## 📝 IMPLEMENTATION CHECKLIST

### Database & Backend

-   [ ] Create migration scripts for new tables
-   [ ] Implement DiscoveryService interface
-   [ ] Create RecommendationService
-   [ ] Build API endpoints
-   [ ] Setup default wishlist logic
-   [ ] Implement user preference system
-   [ ] Add analytics tracking
-   [ ] Performance optimization

### Frontend

-   [ ] Create Discovery button on home page
-   [ ] Build DiscoveryDialog component
-   [ ] Implement DiscoveryCard component
-   [ ] Create CollectionSelectionModal
-   [ ] Add gesture support
-   [ ] Implement animations
-   [ ] Add keyboard navigation
-   [ ] Mobile responsiveness testing

### Testing & QA

-   [ ] Unit tests for services
-   [ ] Integration tests for APIs
-   [ ] E2E tests for user flows
-   [ ] Performance testing
-   [ ] Accessibility testing
-   [ ] Cross-browser testing
-   [ ] Mobile device testing

### Deployment & Monitoring

-   [ ] Setup monitoring dashboards
-   [ ] Configure error tracking
-   [ ] Deploy to staging environment
-   [ ] User acceptance testing
-   [ ] Production deployment
-   [ ] Monitor success metrics
-   [ ] Gather user feedback
-   [ ] Iterate based on data

---

**Tài liệu này sẽ được cập nhật thường xuyên trong quá trình phát triển tính năng Discovery.**
