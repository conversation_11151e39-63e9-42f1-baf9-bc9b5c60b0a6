import { useState, useEffect, useCallback, useRef } from 'react';
import { VOCABULARY_GENERATION_CONFIG } from '@/constants/vocabulary-generation';

interface UseDebouncedSearchOptions {
	delay?: number;
	minLength?: number;
	immediate?: boolean;
}

export function useDebouncedSearch<T>(
	searchFunction: (query: string) => Promise<T[]>,
	options: UseDebouncedSearchOptions = {}
) {
	const {
		delay = VOCABULARY_GENERATION_CONFIG.DEBOUNCE_DELAY,
		minLength = 2,
		immediate = false,
	} = options;

	const [query, setQuery] = useState('');
	const [results, setResults] = useState<T[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
	const abortControllerRef = useRef<AbortController | null>(null);
	const lastSearchRef = useRef<string>('');

	const performSearch = useCallback(
		async (searchQuery: string) => {
			if (searchQuery.length < minLength) {
				setResults([]);
				setIsSearching(false);
				return;
			}

			// Don't search if query hasn't changed
			if (searchQuery === lastSearchRef.current) {
				setIsSearching(false);
				return;
			}

			setIsSearching(true);
			setError(null);
			lastSearchRef.current = searchQuery;

			// Cancel previous request
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}

			// Create new abort controller
			abortControllerRef.current = new AbortController();

			try {
				const searchResults = await searchFunction(searchQuery);
				
				// Only update if this is still the latest search
				if (searchQuery === lastSearchRef.current) {
					setResults(searchResults);
				}
			} catch (err) {
				// Ignore abort errors
				if (err instanceof Error && err.name !== 'AbortError') {
					setError(err.message);
					setResults([]);
				}
			} finally {
				setIsSearching(false);
			}
		},
		[searchFunction, minLength]
	);

	const debouncedSearch = useCallback(
		(searchQuery: string) => {
			// Clear existing timeout
			if (searchTimeoutRef.current) {
				clearTimeout(searchTimeoutRef.current);
			}

			if (immediate && searchQuery.length >= minLength) {
				performSearch(searchQuery);
			} else {
				searchTimeoutRef.current = setTimeout(() => {
					performSearch(searchQuery);
				}, delay);
			}
		},
		[performSearch, delay, immediate, minLength]
	);

	// Effect to trigger search when query changes
	useEffect(() => {
		debouncedSearch(query);

		// Cleanup timeout on unmount
		return () => {
			if (searchTimeoutRef.current) {
				clearTimeout(searchTimeoutRef.current);
			}
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, [query, debouncedSearch]);

	const clearSearch = useCallback(() => {
		setQuery('');
		setResults([]);
		setError(null);
		setIsSearching(false);
		lastSearchRef.current = '';

		if (searchTimeoutRef.current) {
			clearTimeout(searchTimeoutRef.current);
		}
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}
	}, []);

	const setSearchQuery = useCallback((newQuery: string) => {
		setQuery(newQuery);
	}, []);

	return {
		query,
		results,
		isSearching,
		error,
		setQuery: setSearchQuery,
		clearSearch,
		performImmediateSearch: (searchQuery: string) => performSearch(searchQuery),
	};
}