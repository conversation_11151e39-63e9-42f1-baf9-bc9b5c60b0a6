'use client';

import { Button, Translate } from '@/components/ui';
import { RandomWord, WordDetail } from '@/models';
import { Plus, Undo2 } from 'lucide-react';
import { memo } from 'react';

interface WordLoadingState {
	adding?: boolean;
	gettingDetail?: boolean;
	generatingExamples?: boolean;
}

interface WordActionsProps {
	word: RandomWord;
	details: WordDetail | undefined;
	loadingState: WordLoadingState;
	isAdded: boolean;
	onGetDetails: (word: RandomWord) => Promise<void>;
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoWordAddition?: (word: RandomWord) => Promise<void>;
	className?: string;
}

function WordActionsComponent({
	word,
	details,
	loadingState,
	isAdded,
	onGetDetails,
	onAddToCollection,
	onUndoWordAddition,
	className = '',
}: WordActionsProps) {
	return (
		<div className={`flex flex-col space-y-2 ${className}`}>
			{!details && !loadingState.gettingDetail && (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onGetDetails(word)}
					className="w-full flex items-center justify-center gap-2"
				>
					<Translate text="words.get_details" />
				</Button>
			)}
			
			{loadingState.gettingDetail && (
				<Button
					variant="outline"
					size="sm"
					loading={true}
					className="w-full flex items-center justify-center gap-2"
				>
					<Translate text="words.getting_details" />
				</Button>
			)}
			
			{isAdded ? (
				<Button
					variant="outline"
					size="sm"
					onClick={() => onUndoWordAddition?.(word)}
					className="w-full flex items-center justify-center gap-2 border-orange-200 text-orange-700 bg-orange-50 hover:bg-orange-100"
				>
					<Undo2 size={16} />
					<span>
						<Translate text="words.undo" />
					</span>
				</Button>
			) : (
				<Button
					variant="default"
					size="sm"
					onClick={() => onAddToCollection(word)}
					loading={loadingState.adding}
					className="w-full flex items-center justify-center gap-2"
				>
					{!loadingState.adding && <Plus size={16} />}
					<span>
						{loadingState.adding ? (
							<Translate text="words.adding" />
						) : (
							<Translate text="words.add_to_collection" />
						)}
					</span>
				</Button>
			)}
		</div>
	);
}

const arePropsEqual = (prevProps: WordActionsProps, nextProps: WordActionsProps) => {
	return (
		prevProps.word.term === nextProps.word.term &&
		prevProps.details?.id === nextProps.details?.id &&
		prevProps.loadingState.adding === nextProps.loadingState.adding &&
		prevProps.loadingState.gettingDetail === nextProps.loadingState.gettingDetail &&
		prevProps.isAdded === nextProps.isAdded &&
		prevProps.className === nextProps.className &&
		prevProps.onGetDetails === nextProps.onGetDetails &&
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onUndoWordAddition === nextProps.onUndoWordAddition
	);
};

export const WordActions = memo(WordActionsComponent, arePropsEqual);