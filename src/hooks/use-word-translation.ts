import { useCallback, useReducer, useRef } from 'react';
import { Language } from '@prisma/client';
import { WordDetail } from '@/models';
import { getWordTranslationService, TranslateWordsParams } from '@/backend/services/word-translation.service';
import { WordTranslationError, WordDetailsError } from '@/lib/vocabulary-errors';

export interface WordTranslationLoadingState {
	adding: boolean;
	gettingDetail: boolean;
	generatingExamples: boolean;
}

interface WordTranslationState {
	translatedWords: WordDetail[];
	loadingStates: Record<string, WordTranslationLoadingState>;
	addedWords: Set<string>;
	addedWordIds: Record<string, string>;
	isTranslating: boolean;
	error: string | null;
}

type WordTranslationAction =
	| { type: 'SET_TRANSLATING'; payload: boolean }
	| { type: 'SET_TRANSLATED_WORDS'; payload: WordDetail[] }
	| { type: 'APPEND_TRANSLATED_WORDS'; payload: WordDetail[] }
	| { type: 'UPDATE_TRANSLATED_WORD'; payload: { term: string; word: WordDetail } }
	| { type: 'SET_LOADING_STATE'; payload: { term: string; state: Partial<WordTranslationLoadingState> } }
	| { type: 'ADD_WORD'; payload: { term: string; id: string } }
	| { type: 'REMOVE_WORD'; payload: string }
	| { type: 'SET_ERROR'; payload: string | null }
	| { type: 'CLEAR_WORDS' }
	| { type: 'RESET_STATE' };

const initialState: WordTranslationState = {
	translatedWords: [],
	loadingStates: {},
	addedWords: new Set(),
	addedWordIds: {},
	isTranslating: false,
	error: null,
};

function wordTranslationReducer(
	state: WordTranslationState,
	action: WordTranslationAction
): WordTranslationState {
	switch (action.type) {
		case 'SET_TRANSLATING':
			return { ...state, isTranslating: action.payload, error: null };

		case 'SET_TRANSLATED_WORDS':
			return { ...state, translatedWords: action.payload };

		case 'APPEND_TRANSLATED_WORDS': {
			const existingTerms = new Set(state.translatedWords.map(word => word.term));
			const newWords = action.payload.filter(word => !existingTerms.has(word.term));
			return {
				...state,
				translatedWords: [...state.translatedWords, ...newWords],
			};
		}

		case 'UPDATE_TRANSLATED_WORD':
			return {
				...state,
				translatedWords: state.translatedWords.map(word =>
					word.term === action.payload.term ? action.payload.word : word
				),
			};

		case 'SET_LOADING_STATE': {
			const { term, state: loadingState } = action.payload;
			const currentState = state.loadingStates[term] || {
				adding: false,
				gettingDetail: false,
				generatingExamples: false,
			};
			return {
				...state,
				loadingStates: {
					...state.loadingStates,
					[term]: {
						...currentState,
						...loadingState,
					},
				},
			};
		}

		case 'ADD_WORD': {
			const newAddedWords = new Set(state.addedWords);
			newAddedWords.add(action.payload.term);
			return {
				...state,
				addedWords: newAddedWords,
				addedWordIds: {
					...state.addedWordIds,
					[action.payload.term]: action.payload.id,
				},
			};
		}

		case 'REMOVE_WORD': {
			const newAddedWords = new Set(state.addedWords);
			newAddedWords.delete(action.payload);
			const { [action.payload]: _, ...newAddedWordIds } = state.addedWordIds;
			return {
				...state,
				addedWords: newAddedWords,
				addedWordIds: newAddedWordIds,
			};
		}

		case 'SET_ERROR':
			return { ...state, error: action.payload };

		case 'CLEAR_WORDS':
			return { ...state, translatedWords: [] };

		case 'RESET_STATE':
			return initialState;

		default:
			return state;
	}
}

export function useWordTranslation() {
	const [state, dispatch] = useReducer(wordTranslationReducer, initialState);
	const serviceRef = useRef(getWordTranslationService());

	const translateWords = useCallback(async (
		words: string[],
		sourceLanguage: Language,
		targetLanguage: Language,
		appendToList = false
	) => {
		if (state.isTranslating || words.length === 0) return;

		dispatch({ type: 'SET_TRANSLATING', payload: true });

		try {
			const params: TranslateWordsParams = {
				words,
				sourceLanguage,
				targetLanguage,
			};

			const translatedWords = await serviceRef.current.translateWords(params);

			if (appendToList) {
				dispatch({ type: 'APPEND_TRANSLATED_WORDS', payload: translatedWords });
			} else {
				dispatch({ type: 'SET_TRANSLATED_WORDS', payload: translatedWords });
			}

			return translatedWords;
		} catch (error) {
			const errorMessage = error instanceof WordTranslationError 
				? error.message 
				: 'Translation failed';
			dispatch({ type: 'SET_ERROR', payload: errorMessage });
			throw error;
		} finally {
			dispatch({ type: 'SET_TRANSLATING', payload: false });
		}
	}, [state.isTranslating]);

	const getWordDetails = useCallback(async (
		term: string,
		sourceLanguage: Language,
		targetLanguage: Language
	) => {
		const currentLoadingState = state.loadingStates[term];
		if (currentLoadingState?.gettingDetail) return;

		dispatch({
			type: 'SET_LOADING_STATE',
			payload: { term, state: { gettingDetail: true } }
		});

		try {
			const wordDetail = await serviceRef.current.getWordDetails({
				term,
				sourceLanguage,
				targetLanguage,
			});

			dispatch({ type: 'UPDATE_TRANSLATED_WORD', payload: { term, word: wordDetail } });
			return wordDetail;
		} catch (error) {
			const errorMessage = error instanceof WordDetailsError 
				? error.message 
				: 'Failed to get word details';
			dispatch({ type: 'SET_ERROR', payload: errorMessage });
			throw error;
		} finally {
			dispatch({
				type: 'SET_LOADING_STATE',
				payload: { term, state: { gettingDetail: false } }
			});
		}
	}, [state.loadingStates]);

	const setWordLoadingState = useCallback((term: string, loadingState: Partial<WordTranslationLoadingState>) => {
		dispatch({ type: 'SET_LOADING_STATE', payload: { term, state: loadingState } });
	}, []);

	const getWordLoadingState = useCallback((term: string): WordTranslationLoadingState => {
		return state.loadingStates[term] || {
			adding: false,
			gettingDetail: false,
			generatingExamples: false,
		};
	}, [state.loadingStates]);

	const addWord = useCallback((term: string, id: string) => {
		dispatch({ type: 'ADD_WORD', payload: { term, id } });
	}, []);

	const removeWord = useCallback((term: string) => {
		dispatch({ type: 'REMOVE_WORD', payload: term });
	}, []);

	const clearWords = useCallback(() => {
		dispatch({ type: 'CLEAR_WORDS' });
	}, []);

	const resetState = useCallback(() => {
		dispatch({ type: 'RESET_STATE' });
	}, []);

	// Computed properties
	const isAnyLoading = state.isTranslating || 
		Object.values(state.loadingStates).some(loadingState => loadingState.gettingDetail);

	return {
		// State
		translatedWords: state.translatedWords,
		addedWords: state.addedWords,
		addedWordIds: state.addedWordIds,
		isTranslating: state.isTranslating,
		isAnyLoading,
		error: state.error,

		// Actions
		translateWords,
		getWordDetails,
		setWordLoadingState,
		getWordLoadingState,
		addWord,
		removeWord,
		clearWords,
		resetState,
	};
}