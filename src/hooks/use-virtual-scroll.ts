import { useState, useEffect, useCallback, useRef, useMemo } from 'react';

interface UseVirtualScrollOptions {
	itemHeight: number;
	containerHeight: number;
	overscan?: number;
	enabled?: boolean;
}

interface VirtualScrollResult {
	startIndex: number;
	endIndex: number;
	totalHeight: number;
	offsetY: number;
	visibleItems: number;
	containerProps: {
		style: React.CSSProperties;
		onScroll: (event: React.UIEvent<HTMLDivElement>) => void;
	};
	viewportProps: {
		style: React.CSSProperties;
	};
	scrollToItem: (index: number, behavior?: ScrollBehavior) => void;
	scrollToTop: (behavior?: ScrollBehavior) => void;
}

export function useVirtualScroll<T>(
	items: T[],
	options: UseVirtualScrollOptions
): VirtualScrollResult {
	const {
		itemHeight,
		containerHeight,
		overscan = 5,
		enabled = true,
	} = options;

	const [scrollTop, setScrollTop] = useState(0);
	const containerRef = useRef<HTMLDivElement>(null);

	// Calculate visible range
	const { startIndex, endIndex, totalHeight, offsetY, visibleItems } = useMemo(() => {
		if (!enabled || items.length === 0) {
			return {
				startIndex: 0,
				endIndex: items.length,
				totalHeight: items.length * itemHeight,
				offsetY: 0,
				visibleItems: items.length,
			};
		}

		const totalHeight = items.length * itemHeight;
		const visibleItems = Math.ceil(containerHeight / itemHeight);
		
		const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
		const endIndex = Math.min(
			items.length,
			startIndex + visibleItems + overscan * 2
		);
		
		const offsetY = startIndex * itemHeight;

		return {
			startIndex,
			endIndex,
			totalHeight,
			offsetY,
			visibleItems: endIndex - startIndex,
		};
	}, [items.length, itemHeight, containerHeight, scrollTop, overscan, enabled]);

	// Handle scroll events
	const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
		const target = event.currentTarget;
		setScrollTop(target.scrollTop);
	}, []);

	// Scroll to specific item
	const scrollToItem = useCallback((index: number, behavior: ScrollBehavior = 'smooth') => {
		if (containerRef.current) {
			const targetScrollTop = index * itemHeight;
			containerRef.current.scrollTo({
				top: targetScrollTop,
				behavior,
			});
		}
	}, [itemHeight]);

	// Scroll to top
	const scrollToTop = useCallback((behavior: ScrollBehavior = 'smooth') => {
		if (containerRef.current) {
			containerRef.current.scrollTo({
				top: 0,
				behavior,
			});
		}
	}, []);

	// Container props for the scrollable container
	const containerProps = useMemo(() => ({
		ref: containerRef,
		style: {
			height: containerHeight,
			overflow: 'auto' as const,
		},
		onScroll: handleScroll,
	}), [containerHeight, handleScroll]);

	// Viewport props for the inner container that holds the items
	const viewportProps = useMemo(() => ({
		style: {
			height: totalHeight,
			position: 'relative' as const,
		},
	}), [totalHeight]);

	// Content props for the visible items container
	const contentProps = useMemo(() => ({
		style: {
			transform: `translateY(${offsetY}px)`,
			position: 'absolute' as const,
			top: 0,
			width: '100%',
		},
	}), [offsetY]);

	return {
		startIndex,
		endIndex,
		totalHeight,
		offsetY,
		visibleItems,
		containerProps,
		viewportProps,
		scrollToItem,
		scrollToTop,
	};
}