import { Language } from '@prisma/client';
import { WordDetail } from '@/models';
import { VOCABULARY_GENERATION_ENDPOINTS } from '@/constants/vocabulary-generation';
import { WordTranslationError, WordDetailsError, VOCABULARY_ERROR_MESSAGES } from '@/lib/vocabulary-errors';

export interface TranslateWordsParams {
	words: string[];
	sourceLanguage: Language;
	targetLanguage: Language;
}

export interface WordDetailsParams {
	term: string;
	sourceLanguage: Language;
	targetLanguage: Language;
}

export interface WordTranslationService {
	translateWords(params: TranslateWordsParams): Promise<WordDetail[]>;
	getWordDetails(params: WordDetailsParams): Promise<WordDetail>;
	translateSingleWord(word: string, sourceLanguage: Language, targetLanguage: Language): Promise<WordDetail>;
}

class WordTranslationServiceImpl implements WordTranslationService {
	async translateWords(params: TranslateWordsParams): Promise<WordDetail[]> {
		try {
			const response = await fetch(VOCABULARY_GENERATION_ENDPOINTS.TRANSLATE_WORDS, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					words: params.words,
					source_language: params.sourceLanguage,
					target_language: params.targetLanguage,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new WordTranslationError(
					errorData.error || VOCABULARY_ERROR_MESSAGES.TRANSLATION_FAILED
				);
			}

			return response.json();
		} catch (error) {
			if (error instanceof WordTranslationError) {
				throw error;
			}
			throw new WordTranslationError(
				VOCABULARY_ERROR_MESSAGES.TRANSLATION_FAILED,
				error instanceof Error ? error : new Error(String(error))
			);
		}
	}

	async getWordDetails(params: WordDetailsParams): Promise<WordDetail> {
		try {
			const response = await fetch(VOCABULARY_GENERATION_ENDPOINTS.WORD_DETAILS, {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					term: params.term,
					source_language: params.sourceLanguage,
					target_language: params.targetLanguage,
				}),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new WordDetailsError(
					errorData.error || VOCABULARY_ERROR_MESSAGES.WORD_DETAILS_FAILED
				);
			}

			return response.json();
		} catch (error) {
			if (error instanceof WordDetailsError) {
				throw error;
			}
			throw new WordDetailsError(
				VOCABULARY_ERROR_MESSAGES.WORD_DETAILS_FAILED,
				error instanceof Error ? error : new Error(String(error))
			);
		}
	}

	async translateSingleWord(
		word: string,
		sourceLanguage: Language,
		targetLanguage: Language
	): Promise<WordDetail> {
		const result = await this.translateWords({
			words: [word],
			sourceLanguage,
			targetLanguage,
		});

		if (result.length === 0) {
			throw new WordTranslationError(`No translation found for word: ${word}`);
		}

		return result[0];
	}
}

let wordTranslationServiceInstance: WordTranslationService | null = null;

export function getWordTranslationService(): WordTranslationService {
	if (!wordTranslationServiceInstance) {
		wordTranslationServiceInstance = new WordTranslationServiceImpl();
	}
	return wordTranslationServiceInstance;
}