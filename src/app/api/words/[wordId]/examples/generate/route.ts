import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { auth } from '@/lib';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const generateExamplesSchema = z.object({
	definitionId: z.string().min(1, 'Definition ID is required'),
	count: z.number().min(1).max(5).default(3),
});

export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ wordId: string }> }
) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for generating examples.');

		const { wordId } = await params;
		const body = await request.json();
		const validatedData = generateExamplesSchema.parse(body);
		const { definitionId, count } = validatedData;

		const wordService = getWordService();
		const newExamples = await wordService.generateAndSaveExamples(wordId, definitionId, count);

		return NextResponse.json({ 
			examples: newExamples,
			count: newExamples.length,
		});
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Generate examples error:', error);
		return NextResponse.json({ error: 'Failed to generate examples' }, { status: 500 });
	}
}